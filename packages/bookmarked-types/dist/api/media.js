"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MediaFiltersSchema = exports.UpdateMediaSchema = exports.CreateMediaSchema = void 0;
const zod_1 = require("zod");
exports.CreateMediaSchema = zod_1.z.object({
    type: zod_1.z.enum(['book', 'movie']),
    title: zod_1.z.string().min(1, 'Title is required').max(200),
    author: zod_1.z.string().max(100).optional(),
    director: zod_1.z.string().max(100).optional(),
    coverUrl: zod_1.z.string().url().optional(),
    genres: zod_1.z.array(zod_1.z.string()).default([]),
    status: zod_1.z.enum(['want', 'current', 'completed', 'abandoned']).default('want'),
    rating: zod_1.z.number().min(1).max(5).optional(),
    review: zod_1.z.string().max(2000).optional(),
    dateCompleted: zod_1.z.string().datetime().optional(),
    customTags: zod_1.z.array(zod_1.z.string()).default([]),
    isbn: zod_1.z.string().optional(),
    pageCount: zod_1.z.number().positive().optional(),
    publisher: zod_1.z.string().max(100).optional(),
    publishedDate: zod_1.z.string().datetime().optional(),
    imdbId: zod_1.z.string().optional(),
    runtime: zod_1.z.number().positive().optional(),
    releaseYear: zod_1.z.number().min(1800).max(new Date().getFullYear() + 5).optional(),
    cast: zod_1.z.array(zod_1.z.string()).optional(),
    currentPage: zod_1.z.number().positive().optional(),
    watchedMinutes: zod_1.z.number().positive().optional(),
});
exports.UpdateMediaSchema = exports.CreateMediaSchema.partial().extend({
    _id: zod_1.z.string().min(1, 'Media ID is required'),
});
exports.MediaFiltersSchema = zod_1.z.object({
    type: zod_1.z.enum(['book', 'movie']).optional(),
    status: zod_1.z.enum(['want', 'current', 'completed', 'abandoned']).optional(),
    genres: zod_1.z.array(zod_1.z.string()).optional(),
    rating: zod_1.z.number().min(1).max(5).optional(),
    tags: zod_1.z.array(zod_1.z.string()).optional(),
    search: zod_1.z.string().optional(),
    author: zod_1.z.string().optional(),
    director: zod_1.z.string().optional(),
    year: zod_1.z.number().optional(),
    page: zod_1.z.number().positive().default(1),
    limit: zod_1.z.number().min(1).max(100).default(20),
    sortBy: zod_1.z.enum(['title', 'createdAt', 'updatedAt', 'rating', 'dateCompleted']).default('updatedAt'),
    sortOrder: zod_1.z.enum(['asc', 'desc']).default('desc'),
});
//# sourceMappingURL=media.js.map
export interface Genre {
    _id: string;
    name: string;
    type: 'book' | 'movie' | 'both';
    description?: string;
    color?: string;
    isDefault: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export declare const DEFAULT_BOOK_GENRES: readonly ["Fiction", "Non-Fiction", "Mystery", "Romance", "Science Fiction", "Fantasy", "Biography", "History", "Self-Help", "Business", "Health", "Travel", "Cooking", "Art", "Poetry", "Drama", "Horror", "Thriller", "Young Adult", "Children"];
export declare const DEFAULT_MOVIE_GENRES: readonly ["Action", "Adventure", "Animation", "Biography", "Comedy", "Crime", "Documentary", "Drama", "Family", "Fantasy", "History", "Horror", "Music", "Mystery", "Romance", "Science Fiction", "Sport", "Thriller", "War", "Western"];
export type BookGenre = typeof DEFAULT_BOOK_GENRES[number];
export type MovieGenre = typeof DEFAULT_MOVIE_GENRES[number];
//# sourceMappingURL=genre.d.ts.map
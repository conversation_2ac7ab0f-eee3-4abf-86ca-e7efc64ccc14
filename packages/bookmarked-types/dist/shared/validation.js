"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VALIDATION_PATTERNS = exports.createRequiredStringSchema = exports.createOptionalStringSchema = exports.createEnumSchema = exports.MediaTypeSchema = exports.StatusSchema = exports.GenreSchema = exports.SearchQuerySchema = exports.FileTypeSchema = exports.ImageUrlSchema = exports.RatingSchema = exports.TagsSchema = exports.DescriptionSchema = exports.TitleSchema = exports.ObjectIdSchema = exports.DateStringSchema = exports.OptionalUrlSchema = exports.NameSchema = exports.PasswordSchema = exports.EmailSchema = void 0;
const zod_1 = require("zod");
exports.EmailSchema = zod_1.z.string().email("Invalid email format");
exports.PasswordSchema = zod_1.z
    .string()
    .min(8, "Password must be at least 8 characters");
exports.NameSchema = zod_1.z
    .string()
    .min(1, "Name is required")
    .max(50, "Name is too long");
exports.OptionalUrlSchema = zod_1.z
    .string()
    .url("Invalid URL format")
    .optional();
exports.DateStringSchema = zod_1.z.string().datetime("Invalid date format");
exports.ObjectIdSchema = zod_1.z
    .string()
    .regex(/^[0-9a-fA-F]{24}$/, "Invalid ObjectId format");
exports.TitleSchema = zod_1.z
    .string()
    .min(1, "Title is required")
    .max(200, "Title is too long");
exports.DescriptionSchema = zod_1.z
    .string()
    .max(2000, "Description is too long")
    .optional();
exports.TagsSchema = zod_1.z
    .array(zod_1.z.string().min(1).max(50))
    .max(20, "Too many tags");
exports.RatingSchema = zod_1.z
    .number()
    .min(1, "Rating must be at least 1")
    .max(5, "Rating cannot exceed 5");
exports.ImageUrlSchema = zod_1.z.string().url("Invalid image URL").optional();
exports.FileTypeSchema = zod_1.z.enum(["image/jpeg", "image/png", "image/webp"]);
exports.SearchQuerySchema = zod_1.z.string().min(1).max(100);
exports.GenreSchema = zod_1.z.string().min(1).max(50);
exports.StatusSchema = zod_1.z.enum([
    "want",
    "current",
    "completed",
    "abandoned",
]);
exports.MediaTypeSchema = zod_1.z.enum(["book", "movie"]);
const createEnumSchema = (values) => {
    return zod_1.z.enum(values);
};
exports.createEnumSchema = createEnumSchema;
const createOptionalStringSchema = (maxLength = 255) => {
    return zod_1.z.string().max(maxLength).optional();
};
exports.createOptionalStringSchema = createOptionalStringSchema;
const createRequiredStringSchema = (minLength = 1, maxLength = 255) => {
    return zod_1.z.string().min(minLength).max(maxLength);
};
exports.createRequiredStringSchema = createRequiredStringSchema;
exports.VALIDATION_PATTERNS = {
    email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
    isbn: /^(?:ISBN(?:-1[03])?:? )?(?=[0-9X]{10}$|(?=(?:[0-9]+[- ]){3})[- 0-9X]{13}$|97[89][0-9]{10}$|(?=(?:[0-9]+[- ]){4})[- 0-9]{17}$)(?:97[89][- ]?)?[0-9]{1,5}[- ]?[0-9]+[- ]?[0-9]+[- ]?[0-9X]$/,
    imdbId: /^tt[0-9]{7,8}$/,
    mongoObjectId: /^[0-9a-fA-F]{24}$/,
};
//# sourceMappingURL=validation.js.map
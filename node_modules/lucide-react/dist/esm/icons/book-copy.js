/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M2 16V4a2 2 0 0 1 2-2h11", key: "spzkk5" }],
  [
    "path",
    {
      d: "M22 18H11a2 2 0 1 0 0 4h10.5a.5.5 0 0 0 .5-.5v-15a.5.5 0 0 0-.5-.5H11a2 2 0 0 0-2 2v12",
      key: "1wz07i"
    }
  ],
  ["path", { d: "M5 14H4a2 2 0 1 0 0 4h1", key: "16gqf9" }]
];
const BookCopy = createLucideIcon("book-copy", __iconNode);

export { __iconNode, BookCopy as default };
//# sourceMappingURL=book-copy.js.map

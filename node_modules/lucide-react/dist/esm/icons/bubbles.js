/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M7.2 14.8a2 2 0 0 1 2 2", key: "1tw9gg" }],
  ["circle", { cx: "18.5", cy: "8.5", r: "3.5", key: "1wadoa" }],
  ["circle", { cx: "7.5", cy: "16.5", r: "5.5", key: "6mdt3g" }],
  ["circle", { cx: "7.5", cy: "4.5", r: "2.5", key: "637s54" }]
];
const Bubbles = createLucideIcon("bubbles", __iconNode);

export { __iconNode, Bubbles as default };
//# sourceMappingURL=bubbles.js.map

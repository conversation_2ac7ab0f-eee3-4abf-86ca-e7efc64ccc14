export interface PaginationOptions {
    page: number;
    limit: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export interface PaginationResult<T> {
    data: T[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}
export interface SortOptions {
    field: string;
    direction: 'asc' | 'desc';
}
export type CalculatePaginationFn = (total: number, page: number, limit: number) => {
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
    offset: number;
};
export declare const PAGINATION_DEFAULTS: {
    readonly page: 1;
    readonly limit: 20;
    readonly maxLimit: 100;
    readonly sortOrder: "desc";
};
export interface PaginationQuery {
    page?: string | number;
    limit?: string | number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
//# sourceMappingURL=pagination.d.ts.map
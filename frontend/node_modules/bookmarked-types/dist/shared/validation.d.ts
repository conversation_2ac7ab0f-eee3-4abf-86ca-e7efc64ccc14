import { z } from "zod";
export declare const EmailSchema: z.ZodString;
export declare const PasswordSchema: z.ZodString;
export declare const NameSchema: z.ZodString;
export declare const OptionalUrlSchema: z.ZodOptional<z.ZodString>;
export declare const DateStringSchema: z.ZodString;
export declare const ObjectIdSchema: z.ZodString;
export declare const TitleSchema: z.ZodString;
export declare const DescriptionSchema: z.ZodOptional<z.ZodString>;
export declare const TagsSchema: z.ZodArray<z.ZodString, "many">;
export declare const RatingSchema: z.Zod<PERSON>umber;
export declare const ImageUrlSchema: z.ZodOptional<z.ZodString>;
export declare const FileTypeSchema: z.ZodEnum<["image/jpeg", "image/png", "image/webp"]>;
export declare const SearchQuerySchema: z.ZodString;
export declare const GenreSchema: z.ZodString;
export declare const StatusSchema: z.ZodEnum<["want", "current", "completed", "abandoned"]>;
export declare const MediaTypeSchema: z.ZodE<PERSON><["book", "movie"]>;
export declare const createEnumSchema: <T extends readonly [string, ...string[]]>(values: T) => z.ZodEnum<z.Writeable<T>>;
export declare const createOptionalStringSchema: (maxLength?: number) => z.ZodOptional<z.ZodString>;
export declare const createRequiredStringSchema: (minLength?: number, maxLength?: number) => z.ZodString;
export interface ValidationError {
    field: string;
    message: string;
    code: string;
    value?: any;
}
export interface ValidationResult<T> {
    success: boolean;
    data?: T;
    errors?: ValidationError[];
}
export declare const VALIDATION_PATTERNS: {
    readonly email: RegExp;
    readonly password: RegExp;
    readonly isbn: RegExp;
    readonly imdbId: RegExp;
    readonly mongoObjectId: RegExp;
};
//# sourceMappingURL=validation.d.ts.map
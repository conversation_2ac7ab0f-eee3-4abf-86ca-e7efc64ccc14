export interface User {
    _id: string;
    email: string;
    firstName: string;
    lastName: string;
    password: string;
    createdAt: Date;
    updatedAt: Date;
    isActive: boolean;
    emailVerified: boolean;
    lastLogin?: Date;
    preferences: UserPreferences;
}
export interface UserPreferences {
    defaultView: 'grid' | 'list';
    itemsPerPage: number;
    theme: 'light' | 'dark';
    language: string;
    timezone: string;
}
export interface UserDocument extends Omit<User, 'password'> {
    fullName: string;
}
export interface UserProfile {
    _id: string;
    firstName: string;
    lastName: string;
    fullName: string;
    createdAt: Date;
    preferences: Pick<UserPreferences, 'defaultView' | 'theme'>;
}
//# sourceMappingURL=user.d.ts.map
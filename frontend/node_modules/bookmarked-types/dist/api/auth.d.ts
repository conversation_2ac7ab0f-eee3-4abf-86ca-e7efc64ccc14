import { z } from "zod";
import type { UserDocument } from "../database/user";
export declare const RegisterSchema: z.ZodEffects<z.ZodObject<{
    email: z.ZodString;
    firstName: z.ZodString;
    lastName: z.ZodString;
    password: z.ZodString;
    confirmPassword: z.ZodString;
}, "strip", z.ZodTypeAny, {
    password: string;
    email: string;
    firstName: string;
    lastName: string;
    confirmPassword: string;
}, {
    password: string;
    email: string;
    firstName: string;
    lastName: string;
    confirmPassword: string;
}>, {
    password: string;
    email: string;
    firstName: string;
    lastName: string;
    confirmPassword: string;
}, {
    password: string;
    email: string;
    firstName: string;
    lastName: string;
    confirmPassword: string;
}>;
export declare const LoginSchema: z.ZodObject<{
    email: z.ZodString;
    password: z.ZodString;
}, "strip", z.ZodTypeAny, {
    password: string;
    email: string;
}, {
    password: string;
    email: string;
}>;
export declare const UpdateProfileSchema: z.ZodObject<{
    firstName: z.ZodOptional<z.ZodString>;
    lastName: z.ZodOptional<z.ZodString>;
    preferences: z.ZodOptional<z.ZodObject<{
        defaultView: z.ZodOptional<z.ZodEnum<["grid", "list"]>>;
        itemsPerPage: z.ZodOptional<z.ZodNumber>;
        theme: z.ZodOptional<z.ZodEnum<["light", "dark"]>>;
        language: z.ZodOptional<z.ZodString>;
        timezone: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        defaultView?: "grid" | "list" | undefined;
        theme?: "light" | "dark" | undefined;
        itemsPerPage?: number | undefined;
        language?: string | undefined;
        timezone?: string | undefined;
    }, {
        defaultView?: "grid" | "list" | undefined;
        theme?: "light" | "dark" | undefined;
        itemsPerPage?: number | undefined;
        language?: string | undefined;
        timezone?: string | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    firstName?: string | undefined;
    lastName?: string | undefined;
    preferences?: {
        defaultView?: "grid" | "list" | undefined;
        theme?: "light" | "dark" | undefined;
        itemsPerPage?: number | undefined;
        language?: string | undefined;
        timezone?: string | undefined;
    } | undefined;
}, {
    firstName?: string | undefined;
    lastName?: string | undefined;
    preferences?: {
        defaultView?: "grid" | "list" | undefined;
        theme?: "light" | "dark" | undefined;
        itemsPerPage?: number | undefined;
        language?: string | undefined;
        timezone?: string | undefined;
    } | undefined;
}>;
export declare const ChangePasswordSchema: z.ZodEffects<z.ZodObject<{
    currentPassword: z.ZodString;
    newPassword: z.ZodString;
    confirmPassword: z.ZodString;
}, "strip", z.ZodTypeAny, {
    confirmPassword: string;
    currentPassword: string;
    newPassword: string;
}, {
    confirmPassword: string;
    currentPassword: string;
    newPassword: string;
}>, {
    confirmPassword: string;
    currentPassword: string;
    newPassword: string;
}, {
    confirmPassword: string;
    currentPassword: string;
    newPassword: string;
}>;
export type RegisterRequest = z.infer<typeof RegisterSchema>;
export type LoginRequest = z.infer<typeof LoginSchema>;
export type UpdateProfileRequest = z.infer<typeof UpdateProfileSchema>;
export type ChangePasswordRequest = z.infer<typeof ChangePasswordSchema>;
export interface AuthResponse {
    success: boolean;
    message: string;
    data: {
        user: UserDocument;
        token: string;
        expiresIn: string;
    };
}
export interface CookieAuthResponse {
    success: boolean;
    message: string;
    data: {
        user: UserDocument;
    };
}
export interface LoginResponse extends AuthResponse {
}
export interface RegisterResponse extends AuthResponse {
}
export interface CookieLoginResponse extends CookieAuthResponse {
}
export interface CookieRegisterResponse extends CookieAuthResponse {
}
export interface ProfileResponse {
    success: boolean;
    message: string;
    data: {
        user: UserDocument;
    };
}
export interface TokenPayload {
    userId: string;
    email: string;
    iat: number;
    exp: number;
}
//# sourceMappingURL=auth.d.ts.map
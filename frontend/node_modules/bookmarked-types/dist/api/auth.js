"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChangePasswordSchema = exports.UpdateProfileSchema = exports.LoginSchema = exports.RegisterSchema = void 0;
const zod_1 = require("zod");
exports.RegisterSchema = zod_1.z
    .object({
    email: zod_1.z.string().email("Invalid email format"),
    firstName: zod_1.z.string().min(1, "First name is required").max(50),
    lastName: zod_1.z.string().min(1, "Last name is required").max(50),
    password: zod_1.z.string().min(8, "Password must be at least 8 characters"),
    confirmPassword: zod_1.z.string(),
})
    .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
});
exports.LoginSchema = zod_1.z.object({
    email: zod_1.z.string().email("Invalid email format"),
    password: zod_1.z.string().min(1, "Password is required"),
});
exports.UpdateProfileSchema = zod_1.z.object({
    firstName: zod_1.z.string().min(1).max(50).optional(),
    lastName: zod_1.z.string().min(1).max(50).optional(),
    preferences: zod_1.z
        .object({
        defaultView: zod_1.z.enum(["grid", "list"]).optional(),
        itemsPerPage: zod_1.z.number().min(10).max(100).optional(),
        theme: zod_1.z.enum(["light", "dark"]).optional(),
        language: zod_1.z.string().optional(),
        timezone: zod_1.z.string().optional(),
    })
        .optional(),
});
exports.ChangePasswordSchema = zod_1.z
    .object({
    currentPassword: zod_1.z.string().min(1, "Current password is required"),
    newPassword: zod_1.z
        .string()
        .min(8, "New password must be at least 8 characters"),
    confirmPassword: zod_1.z.string().min(1, "Password confirmation is required"),
})
    .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
});
//# sourceMappingURL=auth.js.map
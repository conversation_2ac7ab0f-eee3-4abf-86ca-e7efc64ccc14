import { z } from 'zod';
import type { Media, MediaFilters, MediaStats } from '../database/media';
export declare const CreateMediaSchema: z.ZodObject<{
    type: z.ZodEnum<["book", "movie"]>;
    title: z.ZodString;
    author: <PERSON><PERSON><PERSON><z.ZodString>;
    director: <PERSON><PERSON><z.ZodString>;
    coverUrl: z.<PERSON>od<PERSON>ptional<z.ZodString>;
    genres: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
    status: z.ZodDefault<z.ZodEnum<["want", "current", "completed", "abandoned"]>>;
    rating: z.ZodOptional<z.ZodNumber>;
    review: z.<PERSON>od<PERSON>ptional<z.ZodString>;
    dateCompleted: z.ZodOptional<z.ZodString>;
    customTags: z.ZodDefault<z.ZodArray<z.ZodString, "many">>;
    isbn: z.<PERSON>od<PERSON>ptional<z.ZodString>;
    pageCount: z.ZodOptional<z.ZodNumber>;
    publisher: <PERSON><PERSON><z.ZodString>;
    publishedDate: z.<PERSON><z.ZodString>;
    imdbId: z.ZodOptional<z.ZodString>;
    runtime: z.ZodOptional<z.ZodNumber>;
    releaseYear: z.ZodOptional<z.ZodNumber>;
    cast: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    currentPage: z.ZodOptional<z.ZodNumber>;
    watchedMinutes: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    status: "want" | "current" | "completed" | "abandoned";
    type: "book" | "movie";
    title: string;
    genres: string[];
    customTags: string[];
    author?: string | undefined;
    director?: string | undefined;
    coverUrl?: string | undefined;
    rating?: number | undefined;
    review?: string | undefined;
    dateCompleted?: string | undefined;
    isbn?: string | undefined;
    pageCount?: number | undefined;
    publisher?: string | undefined;
    publishedDate?: string | undefined;
    imdbId?: string | undefined;
    runtime?: number | undefined;
    releaseYear?: number | undefined;
    cast?: string[] | undefined;
    currentPage?: number | undefined;
    watchedMinutes?: number | undefined;
}, {
    type: "book" | "movie";
    title: string;
    status?: "want" | "current" | "completed" | "abandoned" | undefined;
    author?: string | undefined;
    director?: string | undefined;
    coverUrl?: string | undefined;
    genres?: string[] | undefined;
    rating?: number | undefined;
    review?: string | undefined;
    dateCompleted?: string | undefined;
    customTags?: string[] | undefined;
    isbn?: string | undefined;
    pageCount?: number | undefined;
    publisher?: string | undefined;
    publishedDate?: string | undefined;
    imdbId?: string | undefined;
    runtime?: number | undefined;
    releaseYear?: number | undefined;
    cast?: string[] | undefined;
    currentPage?: number | undefined;
    watchedMinutes?: number | undefined;
}>;
export declare const UpdateMediaSchema: z.ZodObject<{
    type: z.ZodOptional<z.ZodEnum<["book", "movie"]>>;
    title: z.ZodOptional<z.ZodString>;
    author: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    director: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    coverUrl: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    genres: z.ZodOptional<z.ZodDefault<z.ZodArray<z.ZodString, "many">>>;
    status: z.ZodOptional<z.ZodDefault<z.ZodEnum<["want", "current", "completed", "abandoned"]>>>;
    rating: z.ZodOptional<z.ZodOptional<z.ZodNumber>>;
    review: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    dateCompleted: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    customTags: z.ZodOptional<z.ZodDefault<z.ZodArray<z.ZodString, "many">>>;
    isbn: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    pageCount: z.ZodOptional<z.ZodOptional<z.ZodNumber>>;
    publisher: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    publishedDate: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    imdbId: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    runtime: z.ZodOptional<z.ZodOptional<z.ZodNumber>>;
    releaseYear: z.ZodOptional<z.ZodOptional<z.ZodNumber>>;
    cast: z.ZodOptional<z.ZodOptional<z.ZodArray<z.ZodString, "many">>>;
    currentPage: z.ZodOptional<z.ZodOptional<z.ZodNumber>>;
    watchedMinutes: z.ZodOptional<z.ZodOptional<z.ZodNumber>>;
} & {
    _id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    _id: string;
    status?: "want" | "current" | "completed" | "abandoned" | undefined;
    type?: "book" | "movie" | undefined;
    title?: string | undefined;
    author?: string | undefined;
    director?: string | undefined;
    coverUrl?: string | undefined;
    genres?: string[] | undefined;
    rating?: number | undefined;
    review?: string | undefined;
    dateCompleted?: string | undefined;
    customTags?: string[] | undefined;
    isbn?: string | undefined;
    pageCount?: number | undefined;
    publisher?: string | undefined;
    publishedDate?: string | undefined;
    imdbId?: string | undefined;
    runtime?: number | undefined;
    releaseYear?: number | undefined;
    cast?: string[] | undefined;
    currentPage?: number | undefined;
    watchedMinutes?: number | undefined;
}, {
    _id: string;
    status?: "want" | "current" | "completed" | "abandoned" | undefined;
    type?: "book" | "movie" | undefined;
    title?: string | undefined;
    author?: string | undefined;
    director?: string | undefined;
    coverUrl?: string | undefined;
    genres?: string[] | undefined;
    rating?: number | undefined;
    review?: string | undefined;
    dateCompleted?: string | undefined;
    customTags?: string[] | undefined;
    isbn?: string | undefined;
    pageCount?: number | undefined;
    publisher?: string | undefined;
    publishedDate?: string | undefined;
    imdbId?: string | undefined;
    runtime?: number | undefined;
    releaseYear?: number | undefined;
    cast?: string[] | undefined;
    currentPage?: number | undefined;
    watchedMinutes?: number | undefined;
}>;
export declare const MediaFiltersSchema: z.ZodObject<{
    type: z.ZodOptional<z.ZodEnum<["book", "movie"]>>;
    status: z.ZodOptional<z.ZodEnum<["want", "current", "completed", "abandoned"]>>;
    genres: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    rating: z.ZodOptional<z.ZodNumber>;
    tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    search: z.ZodOptional<z.ZodString>;
    author: z.ZodOptional<z.ZodString>;
    director: z.ZodOptional<z.ZodString>;
    year: z.ZodOptional<z.ZodNumber>;
    page: z.ZodDefault<z.ZodNumber>;
    limit: z.ZodDefault<z.ZodNumber>;
    sortBy: z.ZodDefault<z.ZodEnum<["title", "createdAt", "updatedAt", "rating", "dateCompleted"]>>;
    sortOrder: z.ZodDefault<z.ZodEnum<["asc", "desc"]>>;
}, "strip", z.ZodTypeAny, {
    page: number;
    limit: number;
    sortBy: "createdAt" | "updatedAt" | "title" | "rating" | "dateCompleted";
    sortOrder: "asc" | "desc";
    status?: "want" | "current" | "completed" | "abandoned" | undefined;
    type?: "book" | "movie" | undefined;
    author?: string | undefined;
    director?: string | undefined;
    genres?: string[] | undefined;
    rating?: number | undefined;
    tags?: string[] | undefined;
    search?: string | undefined;
    year?: number | undefined;
}, {
    status?: "want" | "current" | "completed" | "abandoned" | undefined;
    type?: "book" | "movie" | undefined;
    author?: string | undefined;
    director?: string | undefined;
    genres?: string[] | undefined;
    rating?: number | undefined;
    tags?: string[] | undefined;
    search?: string | undefined;
    year?: number | undefined;
    page?: number | undefined;
    limit?: number | undefined;
    sortBy?: "createdAt" | "updatedAt" | "title" | "rating" | "dateCompleted" | undefined;
    sortOrder?: "asc" | "desc" | undefined;
}>;
export type CreateMediaRequest = z.infer<typeof CreateMediaSchema>;
export type UpdateMediaRequest = z.infer<typeof UpdateMediaSchema>;
export type MediaFiltersRequest = z.infer<typeof MediaFiltersSchema>;
export interface MediaResponse {
    success: boolean;
    message: string;
    data: {
        media: Media;
    };
}
export interface MediaListResponse {
    success: boolean;
    message: string;
    data: {
        media: Media[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
        filters: MediaFilters;
    };
}
export interface MediaStatsResponse {
    success: boolean;
    message: string;
    data: {
        stats: MediaStats;
        breakdown: {
            byType: Record<'book' | 'movie', MediaStats>;
            byStatus: Record<Media['status'], number>;
            byGenre: Record<string, number>;
            byRating: Record<string, number>;
            recentActivity: Media[];
        };
    };
}
export interface DeleteMediaResponse {
    success: boolean;
    message: string;
    data: {
        deletedId: string;
    };
}
//# sourceMappingURL=media.d.ts.map
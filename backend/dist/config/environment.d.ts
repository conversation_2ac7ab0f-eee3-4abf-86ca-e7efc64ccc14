export declare const env: {
    NODE_ENV: "test" | "development" | "production";
    PORT: number;
    MONGODB_URI: string;
    MONGODB_TEST_URI: string;
    JWT_SECRET: string;
    JWT_EXPIRES_IN: string;
    BCRYPT_ROUNDS: number;
    FRONTEND_URL: string;
    ALLOWED_ORIGINS: string;
    RATE_LIMIT_WINDOW_MS: number;
    RATE_LIMIT_MAX_REQUESTS: number;
    SMTP_HOST?: string | undefined;
    SMTP_PORT?: number | undefined;
    SMTP_USER?: string | undefined;
    SMTP_PASS?: string | undefined;
};
export declare const config: {
    readonly app: {
        readonly name: "Bookmarked API";
        readonly version: "1.0.0";
        readonly env: "test" | "development" | "production";
        readonly port: number;
        readonly isDevelopment: boolean;
        readonly isProduction: boolean;
        readonly isTest: boolean;
    };
    readonly database: {
        readonly uri: string;
        readonly options: {
            readonly maxPoolSize: 10;
            readonly serverSelectionTimeoutMS: 5000;
            readonly socketTimeoutMS: 45000;
        };
    };
    readonly auth: {
        readonly jwtSecret: string;
        readonly jwtExpiresIn: string;
        readonly bcryptRounds: number;
    };
    readonly cors: {
        readonly origin: string[];
        readonly credentials: true;
    };
    readonly rateLimit: {
        readonly windowMs: number;
        readonly max: number;
        readonly message: "Too many requests from this IP, please try again later.";
    };
    readonly email: {
        readonly host: string | undefined;
        readonly port: number | undefined;
        readonly user: string | undefined;
        readonly pass: string | undefined;
        readonly enabled: boolean;
    };
};
//# sourceMappingURL=environment.d.ts.map
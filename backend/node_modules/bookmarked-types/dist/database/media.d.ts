export interface Media {
    _id: string;
    userId: string;
    type: 'book' | 'movie';
    title: string;
    author?: string;
    director?: string;
    coverUrl?: string;
    genres: string[];
    status: 'want' | 'current' | 'completed' | 'abandoned';
    rating?: number;
    review?: string;
    dateCompleted?: Date;
    customTags: string[];
    createdAt: Date;
    updatedAt: Date;
    isbn?: string;
    pageCount?: number;
    publisher?: string;
    publishedDate?: Date;
    imdbId?: string;
    runtime?: number;
    releaseYear?: number;
    cast?: string[];
    currentPage?: number;
    watchedMinutes?: number;
    externalIds?: {
        goodreads?: string;
        imdb?: string;
        tmdb?: string;
    };
}
export interface MediaStats {
    totalItems: number;
    completedItems: number;
    currentItems: number;
    wantToReadWatch: number;
    abandonedItems: number;
    averageRating: number;
    totalPages?: number;
    totalMinutes?: number;
}
export interface MediaFilters {
    type?: 'book' | 'movie';
    status?: Media['status'];
    genres?: string[];
    rating?: number;
    tags?: string[];
    search?: string;
    author?: string;
    director?: string;
    year?: number;
}
//# sourceMappingURL=media.d.ts.map